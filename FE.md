Pro Diet: Frontend Requirements (Next.js)
1. Project Overview

This document specifies the frontend requirements for the "Pro Diet" application. The frontend will be a Next.js application providing two primary interfaces: a comprehensive admin dashboard and a self-service customer portal. The application will be built with a focus on modern design, responsiveness, and an excellent user experience.
2. Technology Stack

    Framework: Next.js

    UI Components: Shadcn/UI

    Language: TypeScript

    Styling: Tailwind CSS

3. General UI/UX Requirements

    Language & Direction: The entire user interface must be in Arabic, and all layouts must be configured for Right-to-Left (RTL) text flow.

    Design: The UI must be modern, clean, and visually appealing.

    Responsiveness: All pages and components must be fully responsive, providing a seamless experience on mobile, tablet, and desktop screens.

    Animations: Use subtle, tasteful animations (e.g., page transitions, loading states, button interactions) to enhance the user experience.

    State Management: Utilize a robust state management solution (e.g., Zustand, React Context) for managing application state.

4. Admin Dashboard Features

The admin dashboard is the central control panel for the business.
4.1. Package Management

    A view to display all created meal packages in a table or card layout.

    Forms (within modals or on separate pages) for creating and editing both Standard and Add-On packages.

    Confirmation dialogs for deleting packages.

4.2. Customer & Subscription Management

    A "Customers" section to view and manage all registered users.

    A workflow for the Admin to create a new customer account.

    An interface to assign a subscription package to a customer, setting the start date.

4.3. Production & Kitchen Management Views

    Daily Aggregated Report: A dedicated page that displays the aggregated production report fetched from the backend. This should be clearly formatted and easy to read.

    Per-Customer Breakdown: A view showing the detailed meal breakdown for each customer for the current day. This interface should be printable or easily viewable on a tablet in the kitchen.

5. Customer Portal Features

The customer portal allows clients to manage their subscriptions.
5.1. Authentication

    A login page for customers to enter credentials provided by the admin.

5.2. Dashboard / Home Page

    After logging in, the user should see a summary of their current subscription:

        Package name.

        Subscription start and end dates.

        Days remaining.

5.3. Meal & Preference Management

    An intuitive interface (e.g., a list with checkboxes) for customers to select meals they wish to exclude.

    A text area or form field to input their general dietary preferences and allergies.

5.4. Subscription Pausing

    A dedicated section for managing subscription pauses.

    A calendar or date picker for the customer to select the day(s) they wish to pause.

    The UI must provide clear feedback based on the business rules:

        Dynamically disable dates that are within the 48-hour or 72-hour notice period.

        Display the number of pause days used out of the 7 available.

        Show a clear confirmation message upon a successful pause request.

5.5. Delivery & Pickup Options

    During the onboarding process (handled by the admin) or in the customer profile, the customer's choice for delivery or pickup must be stored and displayed.

5.6. Friday Meal Preference

    The UI should present a clear, one-time choice for the customer regarding their Friday meals (e.g., during their first login or on their profile page).
