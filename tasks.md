Pro Diet: Frontend Development Task List (Next.js)

This task list breaks down the frontend development into actionable steps. It is designed to complement the backend task list and ensure a logical development process.
Phase 1: Project Setup & Foundation

    [ ] 1.1: Initialize Next.js Project

        Create a new Next.js project using create-next-app in sthe same folder.

        Configure the project to use TypeScript.

    [ ] 1.2: UI & Styling Setup

        Integrate Tailwind CSS for styling.

        Initialize Shadcn/UI and add a few basic components to start (e.g., Button, Card, Input).

        Configure the entire application for Arabic language and RTL (Right-to-Left) layout. This includes setting the dir="rtl" attribute on the <html> tag and adjusting Tailwind configuration if necessary.

    [ ] 1.3: Create Core Layout Components

        Develop a main Layout component that includes a shared header, footer, and sidebar/navigation.

        Create separate layout structures for the Admin Dashboard and the Customer Portal.

        Implement a global state management solution (e.g., Zustand or React Context) to handle user authentication state and other global data.

Phase 2: Authentication & Shared Components

    [ ] 2.1: Implement Login Page

        Create the UI for the login page with fields for username/email and password.

        Implement the logic to call the backend authentication endpoint and store the JWT upon successful login.

    [ ] 2.2: Implement Routing & Protected Routes

        Set up page-based routing for all admin and customer views.

        Create a mechanism for protected routes that redirects unauthenticated users to the login page.

Phase 3: Admin Dashboard Implementation

    [ ] 3.1: Package Management UI

        Create a page to display all meal packages using Shadcn Table or Card components.

        Build reusable Form components (using Shadcn Dialog or a separate page) for creating and editing packages.

        Connect the UI to the backend package management endpoints (GET, POST, PUT, DELETE).

    [ ] 3.2: Customer & Subscription UI (Admin)

        Create a page to display a list of all customers.

        Build the UI flow for an Admin to create a new customer account.

        Develop the interface for an Admin to assign a subscription to a customer, including a date picker for the start date and a dropdown for the package.

        Connect these components to the relevant backend endpoints.

Phase 4: Production Reporting UI (Admin)

    [ ] 4.1: Aggregated Production Report View

        Design and build a page to clearly display the aggregated daily production data from the GET /production/today/aggregated endpoint.

        Ensure the data is presented in a simple, easy-to-read table format.

    [ ] 4.2: Customer Breakdown View

        Design and build a page to display the detailed per-customer meal breakdown from the GET /production/today/customer-breakdown endpoint.

        The UI should be clean and optimized for being read quickly in a kitchen environment (e.g., on a tablet) or for printing.

Phase 5: Customer Portal Implementation

    [ ] 5.1: Customer Dashboard

        Create the main dashboard page for a logged-in customer, displaying a summary of their subscription (package name, end date, etc.).

    [ ] 5.2: Meal & Preference Management UI

        Build the UI for customers to manage their meal exclusions (e.g., a list of meals with checkboxes).

        Create the UI for customers to update their dietary preferences.

        Connect this to the PUT /customers/{id}/preferences endpoint.

    [ ] 5.3: Subscription Pause UI

        Design a dedicated page for pausing a subscription.

        Use a calendar component (e.g., from Shadcn) for date selection.

        UI Logic:

            Dynamically disable dates in the calendar that fall within the 48/72-hour restricted period.

            Display a counter for remaining pause days (7 - used days).

            Provide clear success or error feedback to the user after a pause request.

        Connect the UI to the POST /subscriptions/{id}/pause endpoint.

Phase 6: Finalization & Polish

    [ ] 6.1: Full API Integration

        Ensure all forms, tables, and views are correctly fetching and sending data to the backend.

        Implement loading states (spinners, skeletons) for all asynchronous operations.

        Implement comprehensive error handling to show user-friendly messages for API errors.

    [ ] 6.2: Responsiveness & Animations

        Thoroughly test the entire application across mobile, tablet, and desktop screen sizes.

        Add subtle animations and transitions to improve the overall user experience.

    [ ] 6.3: Final Review

        Review all UI components for consistency and adherence to the modern design requirement.

        Test all user flows from both an Admin and Customer perspective.
