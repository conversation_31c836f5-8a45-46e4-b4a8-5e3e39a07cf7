Pro Diet: Customer & Production Management System - Project Requirements
1. Project Overview

This document outlines the requirements for "Pro Diet," a full-stack web application designed to manage customer subscriptions, meal customization, and daily food production for a health-focused restaurant. The system will provide a comprehensive admin dashboard for managing all aspects of the business and a client-facing portal for customers to manage their subscriptions.
2. Technology Stack

    Backend: Spring Boot

    Database: SQLite

    Frontend: Next.js

    UI Framework: Shadcn/UI

    API Documentation: Swagger

3. Core Features
3.1. Package Management (Admin)

The system must allow an administrator to create and manage two types of meal packages.
3.1.1. Standard Subscription Packages

    Attributes:

        Package Name (e.g., "Monthly Weight Loss", "Athlete Bulk-Up")

        Duration (in days)

        Daily Protein allowance (in grams)

        Daily Carbohydrate allowance (in grams)

        Number of meals per day

3.1.2. Add-On Packages

    Attributes:

        Package Name (e.g., "Weekly Salad Pack", "Snack Box")

        Number of meals per week

        Frequency (how many times per week meals are delivered/produced)

        Specific production days for the meals.

3.2. Customer & Subscription Management (Admin)

    Admin Dashboard: The Admin will have full control over the system via a comprehensive control panel.

    Customer Onboarding: The Admin is responsible for creating customer accounts and registering them for their chosen subscription package.

    Subscription Assignment: The Admin assigns a specific subscription package to a customer, which marks the start of their service.

3.3. Customer Portal (Client)

Customers will log into a dedicated portal using credentials provided by the Admin.

    Meal Exclusion: Customers can view the menu and select specific meals they wish to exclude from their plan.

    Dietary Preferences: Customers can specify general dislikes or allergies (e.g., "no eggs," "not spicy," "lactose intolerant"). This information should serve as a flag for the kitchen.

    Subscription Pausing: Customers can request to temporarily pause their subscription.

3.4. Production & Kitchen Management (Admin)

This is a critical feature for managing daily kitchen operations.

    Daily Aggregated Production Report:

        The system must generate a daily report that sums the total weight of every food item required for all active subscriptions.

        Example:

            Rice: 2250g

            Chinese Chicken: 1650g

            Molokhia: 2000g

    Per-Customer Daily Meal Breakdown:

        The system must generate a detailed packing list for each customer's meals for the day.

        This list should specify each meal, its components, and the precise weight of each component.

        Example: Customer: Ahmed (2 Meals Today)

            Meal 1: 150g Rice, 100g Chinese Chicken, 100g Salad

            Meal 2: 100g Bread, 150g Kofta

3.5. Delivery & Pickup

    Customers must be able to choose their preferred method for receiving meals:

        Delivery to a specified address.

        Pickup from a restaurant branch.

4. Business Rules & Logic
4.1. Subscription Pause Rules

    A customer can pause their subscription for a maximum of 7 days per single subscription term.

    A pause request must be submitted at least 48 hours before the start of the day to be paused.

    Weekend Exception: If the day to be paused is a Saturday, the request must be submitted at least 72 hours in advance.

    When a subscription is successfully paused, the paused days are marked accordingly, and the subscription's end date is automatically extended by the number of days paused.

4.2. Friday Service Rules (Restaurant Closed)

The restaurant does not operate on Fridays. The system must handle this in one of two ways based on customer choice:

    Option 1: Receive Friday Meals on Thursday

        If the customer opts-in, they will receive double their daily meal quota on the preceding Thursday.

        Example: If a customer's plan includes 2 meals per day, they will receive 4 meals on Thursday to cover both Thursday and Friday. The system must reflect this in the Thursday production reports.

    Option 2: Skip Friday Meals & Extend Subscription

        If the customer does not opt-in to receive Friday's meals, their subscription end date is extended by one day for every Friday that occurs during their subscription period.

        Example: A one-month subscription that includes four Fridays will have its end date extended by 4 days.

5. Non-Functional Requirements
5.1. Code Quality & Design

    The application must be built following industry best practices.

    Code must adhere to SOLID and DRY (Don't Repeat Yourself) principles.

5.2. User Interface (UI) & User Experience (UX)

    The UI must have a modern and aesthetically pleasing design.

    The application must be fully responsive and function flawlessly on desktop, tablet, and mobile devices.

    Subtle animations should be used to enhance the user experience.

    The frontend application language must be Arabic, and the entire layout must support Right-to-Left (RTL) direction.
