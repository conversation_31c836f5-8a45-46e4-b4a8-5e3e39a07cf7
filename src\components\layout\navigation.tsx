'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useAuthStore } from '@/lib/store'
import { cn } from '@/lib/utils'

interface NavigationItem {
  href: string
  label: string
  icon?: React.ReactNode
}

const adminNavItems: NavigationItem[] = [
  { href: '/admin', label: 'لوحة التحكم' },
  { href: '/admin/packages', label: 'إدارة الباقات' },
  { href: '/admin/customers', label: 'إدارة العملاء' },
  { href: '/admin/subscriptions', label: 'إدارة الاشتراكات' },
  { href: '/admin/production', label: 'تقارير الإنتاج' },
]

const customerNavItems: NavigationItem[] = [
  { href: '/customer', label: 'لوحة التحكم' },
  { href: '/customer/preferences', label: 'تفضيلات الوجبات' },
  { href: '/customer/pause', label: 'إيقاف الاشتراك' },
]

export function Navigation() {
  const pathname = usePathname()
  const { user, logout } = useAuthStore()

  if (!user) return null

  const navItems = user.role === 'ADMIN' ? adminNavItems : customerNavItems

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href={user.role === 'ADMIN' ? '/admin' : '/customer'}>
                <h1 className="text-xl font-bold text-primary">برو دايت</h1>
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8 sm:space-x-reverse">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium',
                    pathname === item.href
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
          <div className="flex items-center space-x-4 space-x-reverse">
            <span className="text-sm text-gray-700">مرحباً، {user.name}</span>
            <Button variant="outline" onClick={logout}>
              تسجيل الخروج
            </Button>
          </div>
        </div>
      </div>
    </nav>
  )
}
