{"openapi": "3.0.1", "info": {"title": "Pro Diet API", "description": "Customer & Production Management System for Pro Diet Restaurant", "contact": {"name": "Pro Diet Development Team", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "v1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Development Server"}], "security": [{"Bearer Authentication": []}], "tags": [{"name": "Production Reports", "description": "Production reporting APIs for administrators"}, {"name": "Customer Management", "description": "Customer management APIs for administrators"}, {"name": "Authentication", "description": "Authentication management APIs"}, {"name": "Subscription Management", "description": "Subscription management APIs"}, {"name": "Package Management", "description": "Package management APIs for administrators"}, {"name": "Subscription Pause Management", "description": "Subscription pause management APIs for customers"}, {"name": "Customer Subscription Retrieval", "description": "Customer subscription retrieval APIs"}, {"name": "Customer Preferences", "description": "Customer preference management APIs"}], "paths": {"/api/v1/subscriptions/{id}/cancel": {"put": {"tags": ["Subscription Management"], "summary": "Cancel subscription", "description": "Cancel a subscription (Admin only)", "operationId": "cancelSubscription", "parameters": [{"name": "id", "in": "path", "description": "Subscription ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/packages/{id}": {"get": {"tags": ["Package Management"], "summary": "Get package by ID", "description": "Retrieve a specific package by its ID", "operationId": "getPackageById", "parameters": [{"name": "id", "in": "path", "description": "Package ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PackageResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Package Management"], "summary": "Update package", "description": "Update an existing package", "operationId": "updatePackage", "parameters": [{"name": "id", "in": "path", "description": "Package ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackageRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PackageResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "delete": {"tags": ["Package Management"], "summary": "Delete package", "description": "Soft delete a package (mark as inactive)", "operationId": "deletePackage", "parameters": [{"name": "id", "in": "path", "description": "Package ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{id}": {"get": {"tags": ["Customer Management"], "summary": "Get customer by ID", "description": "Retrieve a specific customer by their ID", "operationId": "getCustomerById", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Customer Management"], "summary": "Update customer", "description": "Update an existing customer's information", "operationId": "updateCustomer", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "delete": {"tags": ["Customer Management"], "summary": "Delete customer", "description": "Soft delete a customer (mark as inactive)", "operationId": "deleteCustomer", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{id}/preferences": {"get": {"tags": ["Customer Preferences"], "summary": "Get customer preferences", "description": "Retrieve meal exclusions and dietary preferences for a customer", "operationId": "getCustomerPreferences", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PreferencesResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Customer Preferences"], "summary": "Update customer preferences", "description": "Update meal exclusions and dietary preferences for a customer", "operationId": "updateCustomerPreferences", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePreferencesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PreferencesResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions": {"get": {"tags": ["Subscription Management"], "summary": "Get all subscriptions", "description": "Retrieve all subscriptions (Admin only)", "operationId": "getAllSubscriptions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}, "post": {"tags": ["Subscription Management"], "summary": "Create new subscription", "description": "Create a new subscription for a customer (Admin only)", "operationId": "createSubscription", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/packages": {"get": {"tags": ["Package Management"], "summary": "Get all packages", "description": "Retrieve all active packages", "operationId": "getAllPackages", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PackageResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}, "post": {"tags": ["Package Management"], "summary": "Create new package", "description": "Create a new meal package", "operationId": "createPackage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackageRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PackageResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers": {"get": {"tags": ["Customer Management"], "summary": "Get all customers", "description": "Retrieve all active customers", "operationId": "getAllCustomers", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}, "post": {"tags": ["Customer Management"], "summary": "Create new customer", "description": "Create a new customer account", "operationId": "createCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{customerId}/subscription/validate-pause": {"post": {"tags": ["Subscription Pause Management"], "summary": "Validate pause request", "description": "Validate if a pause request is allowed without actually pausing", "operationId": "validatePauseRequest", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "pauseDate", "in": "query", "description": "Pause date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PauseValidationResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{customerId}/subscription/pause": {"post": {"tags": ["Subscription Pause Management"], "summary": "Pause subscription", "description": "Pause a customer's subscription for a specific date with validation rules", "operationId": "pauseSubscription", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PauseSubscriptionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PausedDayResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/auth/validate": {"post": {"tags": ["Authentication"], "summary": "Validate JWT token", "description": "Validate if the provided JWT token is valid", "operationId": "validateToken", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "boolean"}}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return JWT token", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}}}, "/api/v1/subscriptions/{id}": {"get": {"tags": ["Subscription Management"], "summary": "Get subscription by ID", "description": "Retrieve a specific subscription by its ID (Admin only)", "operationId": "getSubscriptionById", "parameters": [{"name": "id", "in": "path", "description": "Subscription ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions/{id}/paused-days": {"get": {"tags": ["Customer Subscription Retrieval"], "summary": "Get subscription paused days", "description": "Retrieve all paused days for a subscription", "operationId": "getSubscriptionPausedDays", "parameters": [{"name": "id", "in": "path", "description": "Subscription ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PausedDayResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions/customer/{customerId}": {"get": {"tags": ["Subscription Management"], "summary": "Get subscriptions by customer", "description": "Retrieve all subscriptions for a specific customer (Admin only)", "operationId": "getSubscriptionsByCustomerId", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions/customer/{customerId}/active": {"get": {"tags": ["Subscription Management"], "summary": "Get active subscription by customer", "description": "Retrieve the active subscription for a specific customer", "operationId": "getActiveSubscriptionByCustomerId", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions/count/active": {"get": {"tags": ["Subscription Management"], "summary": "Get active subscription count", "description": "Get the total number of active subscriptions (Admin only)", "operationId": "getActiveSubscriptionCount", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions/active": {"get": {"tags": ["Subscription Management"], "summary": "Get all active subscriptions", "description": "Retrieve all active subscriptions (Admin only)", "operationId": "getActiveSubscriptions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/subscriptions/active/date/{date}": {"get": {"tags": ["Subscription Management"], "summary": "Get active subscriptions for date", "description": "Retrieve all active subscriptions for a specific date (Admin only)", "operationId": "getActiveSubscriptionsForDate", "parameters": [{"name": "date", "in": "path", "description": "Date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/today/full-report": {"get": {"tags": ["Production Reports"], "summary": "Get today's complete production report", "description": "Get complete production report including aggregated ingredients and customer breakdown", "operationId": "getTodayFullReport", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductionReportDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/today/customer-breakdown": {"get": {"tags": ["Production Reports"], "summary": "Get today's customer breakdown report", "description": "Get detailed meal-by-meal, component-by-component breakdown for each customer", "operationId": "getTodayCustomerBreakdown", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerMealDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/today/aggregated": {"get": {"tags": ["Production Reports"], "summary": "Get today's aggregated production report", "description": "Get aggregated ingredient totals for today's production", "operationId": "getTodayAggregatedReport", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IngredientSummaryDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/summary": {"get": {"tags": ["Production Reports"], "summary": "Get production summary", "description": "Get a quick summary of today's production requirements", "operationId": "getProductionSummary", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/stats": {"get": {"tags": ["Production Reports"], "summary": "Get production statistics", "description": "Get production statistics for a date range", "operationId": "getProductionStats", "parameters": [{"name": "startDate", "in": "query", "description": "Start date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/ingredients/most-used": {"get": {"tags": ["Production Reports"], "summary": "Get most used ingredients", "description": "Get the most frequently used ingredients across all active subscriptions", "operationId": "getMostUsedIngredients", "parameters": [{"name": "limit", "in": "query", "description": "Number of ingredients to return", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IngredientSummaryDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/date/{date}/full-report": {"get": {"tags": ["Production Reports"], "summary": "Get complete production report for specific date", "description": "Get complete production report for a specific date", "operationId": "getFullReportForDate", "parameters": [{"name": "date", "in": "path", "description": "Date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductionReportDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/date/{date}/customer-breakdown": {"get": {"tags": ["Production Reports"], "summary": "Get customer breakdown report for specific date", "description": "Get detailed meal-by-meal, component-by-component breakdown for a specific date", "operationId": "getCustomerBreakdownForDate", "parameters": [{"name": "date", "in": "path", "description": "Date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerMealDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/production/date/{date}/aggregated": {"get": {"tags": ["Production Reports"], "summary": "Get aggregated production report for specific date", "description": "Get aggregated ingredient totals for a specific date", "operationId": "getAggregatedReportForDate", "parameters": [{"name": "date", "in": "path", "description": "Date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IngredientSummaryDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/packages/type/{type}": {"get": {"tags": ["Package Management"], "summary": "Get packages by type", "description": "Retrieve packages filtered by type (STANDARD or ADD_ON)", "operationId": "getPackagesByType", "parameters": [{"name": "type", "in": "path", "description": "Package type", "required": true, "schema": {"type": "string", "enum": ["STANDARD", "ADD_ON"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PackageResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{id}/subscription": {"get": {"tags": ["Customer Subscription Retrieval"], "summary": "Get customer subscription", "description": "Retrieve the active subscription details for a customer", "operationId": "getCustomerSubscription", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CustomerSubscriptionResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{id}/paused-days": {"get": {"tags": ["Customer Subscription Retrieval"], "summary": "Get customer paused days", "description": "Retrieve all paused days for a customer's active subscription", "operationId": "getCustomerPausedDays", "parameters": [{"name": "id", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PausedDayResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{customerId}/subscription/available-pause-dates": {"get": {"tags": ["Subscription Pause Management"], "summary": "Get available pause dates", "description": "Get all available dates that can be paused for the customer's subscription", "operationId": "getAvailablePauseDates", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string", "format": "date"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/search": {"get": {"tags": ["Customer Management"], "summary": "Search customers", "description": "Search customers by name or email", "operationId": "searchCustomers", "parameters": [{"name": "q", "in": "query", "description": "Search term", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/count": {"get": {"tags": ["Customer Management"], "summary": "Get customer count", "description": "Get the total number of active customers", "operationId": "getCustomerCount", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/customers/{customerId}/subscription/pause/{pauseDate}": {"delete": {"tags": ["Subscription Pause Management"], "summary": "Remove paused day", "description": "Remove a future paused day from the subscription", "operationId": "removePausedDay", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "pauseDate", "in": "path", "description": "Pause date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK"}}, "security": [{"Bearer Authentication": []}]}}}, "components": {"schemas": {"PackageRequest": {"required": ["name", "price", "type"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["STANDARD", "ADD_ON"]}, "durationDays": {"minimum": 1, "type": "integer", "format": "int32"}, "dailyProteinG": {"minimum": 0, "type": "integer", "format": "int32"}, "dailyCarbsG": {"minimum": 0, "type": "integer", "format": "int32"}, "mealsPerDay": {"minimum": 1, "type": "integer", "format": "int32"}, "mealsPerWeek": {"minimum": 1, "type": "integer", "format": "int32"}, "frequencyPerWeek": {"minimum": 1, "type": "integer", "format": "int32"}, "productionDays": {"type": "string"}, "price": {"minimum": 0, "type": "number", "format": "double"}}}, "PackageResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["STANDARD", "ADD_ON"]}, "durationDays": {"type": "integer", "format": "int32"}, "dailyProteinG": {"type": "integer", "format": "int32"}, "dailyCarbsG": {"type": "integer", "format": "int32"}, "mealsPerDay": {"type": "integer", "format": "int32"}, "mealsPerWeek": {"type": "integer", "format": "int32"}, "frequencyPerWeek": {"type": "integer", "format": "int32"}, "productionDays": {"type": "string"}, "price": {"type": "number", "format": "double"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "CreateCustomerRequest": {"required": ["email", "name", "password"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string"}, "email": {"type": "string"}, "password": {"maxLength": 2147483647, "minLength": 6, "type": "string"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string"}, "address": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "UserResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string", "enum": ["ADMIN", "CUSTOMER"]}, "phoneNumber": {"type": "string"}, "address": {"type": "string"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "DietaryPreferenceDto": {"required": ["preferenceText"], "type": "object", "properties": {"preferenceText": {"type": "string"}, "preferenceType": {"type": "string", "enum": ["ALLERGY", "DISLIKE", "DIETARY_RESTRICTION", "OTHER"]}}}, "UpdatePreferencesRequest": {"required": ["dietaryPreferences", "excludedMealIds"], "type": "object", "properties": {"excludedMealIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "dietaryPreferences": {"type": "array", "items": {"$ref": "#/components/schemas/DietaryPreferenceDto"}}}}, "ExcludedMealDto": {"type": "object", "properties": {"mealId": {"type": "integer", "format": "int64"}, "mealName": {"type": "string"}, "mealType": {"type": "string"}}}, "PreferencesResponse": {"type": "object", "properties": {"excludedMeals": {"type": "array", "items": {"$ref": "#/components/schemas/ExcludedMealDto"}}, "dietaryPreferences": {"type": "array", "items": {"$ref": "#/components/schemas/DietaryPreferenceDto"}}}}, "CreateSubscriptionRequest": {"required": ["customerId", "packageId", "startDate"], "type": "object", "properties": {"customerId": {"type": "integer", "format": "int64"}, "packageId": {"type": "integer", "format": "int64"}, "startDate": {"type": "string", "format": "date"}, "fridayPreference": {"type": "string", "enum": ["RECEIVE_ON_THURSDAY", "SKIP_AND_EXTEND"]}, "deliveryMethod": {"type": "string", "enum": ["DELIVERY", "PICKUP"]}, "deliveryAddress": {"maxLength": 500, "minLength": 0, "type": "string"}, "deliveryNotes": {"maxLength": 200, "minLength": 0, "type": "string"}}}, "SubscriptionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "customerId": {"type": "integer", "format": "int64"}, "customerName": {"type": "string"}, "customerEmail": {"type": "string"}, "packageId": {"type": "integer", "format": "int64"}, "packageName": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "status": {"type": "string", "enum": ["ACTIVE", "PAUSED", "COMPLETED", "CANCELLED"]}, "fridayPreference": {"type": "string", "enum": ["RECEIVE_ON_THURSDAY", "SKIP_AND_EXTEND"]}, "deliveryMethod": {"type": "string", "enum": ["DELIVERY", "PICKUP"]}, "deliveryAddress": {"type": "string"}, "deliveryNotes": {"type": "string"}, "totalPausedDays": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "PauseValidationResponse": {"type": "object", "properties": {"canPause": {"type": "boolean"}, "message": {"type": "string"}, "violations": {"type": "array", "items": {"type": "string"}}, "remainingPauseDays": {"type": "integer", "format": "int32"}, "earliestPauseDate": {"type": "string", "format": "date"}}}, "PauseSubscriptionRequest": {"required": ["pauseDate"], "type": "object", "properties": {"pauseDate": {"type": "string", "format": "date"}, "reason": {"maxLength": 200, "minLength": 0, "type": "string"}}}, "PausedDayResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "pausedDate": {"type": "string", "format": "date"}, "reason": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}}}, "LoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "LoginResponse": {"type": "object", "properties": {"token": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string"}}}, "ComponentDetailDto": {"type": "object", "properties": {"ingredientName": {"type": "string"}, "weightG": {"type": "integer", "format": "int32"}, "totalWeightG": {"type": "integer", "format": "int32"}, "preparationNotes": {"type": "string"}}}, "CustomerMealDto": {"type": "object", "properties": {"customerId": {"type": "integer", "format": "int64"}, "customerName": {"type": "string"}, "customerEmail": {"type": "string"}, "mealMultiplier": {"type": "integer", "format": "int32"}, "fridayPreference": {"type": "string"}, "meals": {"type": "array", "items": {"$ref": "#/components/schemas/MealDetailDto"}}}}, "IngredientSummaryDto": {"type": "object", "properties": {"ingredientName": {"type": "string"}, "totalWeightG": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}}}, "MealDetailDto": {"type": "object", "properties": {"mealId": {"type": "integer", "format": "int64"}, "mealName": {"type": "string"}, "mealType": {"type": "string"}, "components": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentDetailDto"}}}}, "ProductionReportDto": {"type": "object", "properties": {"reportDate": {"type": "string", "format": "date"}, "totalActiveSubscriptions": {"type": "integer", "format": "int32"}, "totalMealsToDeliver": {"type": "integer", "format": "int32"}, "ingredientSummary": {"type": "array", "items": {"$ref": "#/components/schemas/IngredientSummaryDto"}}, "customerBreakdown": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerMealDto"}}}}, "CustomerSubscriptionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "packageName": {"type": "string"}, "packageDescription": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "status": {"type": "string"}, "fridayPreference": {"type": "string"}, "deliveryMethod": {"type": "string"}, "deliveryAddress": {"type": "string"}, "deliveryNotes": {"type": "string"}, "totalPausedDays": {"type": "integer", "format": "int32"}, "remainingPauseDays": {"type": "integer", "format": "int32"}, "packageDetails": {"$ref": "#/components/schemas/PackageDetailsDto"}}}, "PackageDetailsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "type": {"type": "string"}, "durationDays": {"type": "integer", "format": "int32"}, "dailyProteinG": {"type": "integer", "format": "int32"}, "dailyCarbsG": {"type": "integer", "format": "int32"}, "mealsPerDay": {"type": "integer", "format": "int32"}, "price": {"type": "number", "format": "double"}}}}, "securitySchemes": {"Bearer Authentication": {"type": "http", "description": "JWT token for authentication", "scheme": "bearer", "bearerFormat": "JWT"}}}}