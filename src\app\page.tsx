'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/store'
import { PageLoading } from '@/components/ui/loading'

export default function Home() {
  const { isAuthenticated, user } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
    } else if (user?.role === 'ADMIN') {
      router.push('/admin')
    } else if (user?.role === 'CUSTOMER') {
      router.push('/customer')
    }
  }, [isAuthenticated, user, router])

  return <PageLoading />
}
