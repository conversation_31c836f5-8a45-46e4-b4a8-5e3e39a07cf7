'use client'

import { CustomerLayout } from '@/components/layout/customer-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function CustomerDashboard() {
  return (
    <CustomerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم العميل</h1>
          <p className="mt-2 text-gray-600">
            مرحباً بك في بوابة العملاء لمطعم برو دايت
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>معلومات الاشتراك</CardTitle>
              <CardDescription>
                تفاصيل اشتراكك الحالي
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">اسم الباقة</div>
                  <div className="font-medium">--</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">تاريخ البداية</div>
                  <div className="font-medium">--</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">تاريخ الانتهاء</div>
                  <div className="font-medium">--</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">الأيام المتبقية</div>
                  <div className="font-medium">--</div>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="text-sm text-gray-600 mb-2">حالة الاشتراك</div>
                <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  نشط
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>الإجراءات السريعة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild className="w-full justify-start">
                <a href="/customer/preferences">
                  إدارة تفضيلات الوجبات
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/customer/pause">
                  إيقاف الاشتراك مؤقتاً
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>تفضيلات الوجبات</CardTitle>
              <CardDescription>
                الوجبات المستبعدة والتفضيلات الغذائية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <div className="text-sm text-gray-600">الوجبات المستبعدة</div>
                  <div className="text-sm">لم يتم استبعاد أي وجبات</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">التفضيلات الغذائية</div>
                  <div className="text-sm">لم يتم تحديد تفضيلات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>أيام الإيقاف</CardTitle>
              <CardDescription>
                الأيام التي تم إيقاف الاشتراك فيها
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-gray-500 py-4">
                لم يتم إيقاف أي أيام
              </div>
              <div className="text-xs text-gray-500 text-center">
                يمكنك إيقاف الاشتراك لمدة تصل إلى 7 أيام
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CustomerLayout>
  )
}
