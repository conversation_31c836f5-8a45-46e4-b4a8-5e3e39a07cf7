const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1'

export class ApiClient {
  private baseUrl: string
  private token: string | null = null

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  setToken(token: string | null) {
    this.token = token
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`API Error: ${response.status} - ${error}`)
    }

    return response.json()
  }

  // Authentication
  async login(email: string, password: string) {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  }

  async validateToken() {
    return this.request('/auth/validate', {
      method: 'POST',
    })
  }

  // Packages
  async getPackages() {
    return this.request('/packages')
  }

  async createPackage(packageData: any) {
    return this.request('/packages', {
      method: 'POST',
      body: JSON.stringify(packageData),
    })
  }

  async updatePackage(id: number, packageData: any) {
    return this.request(`/packages/${id}`, {
      method: 'PUT',
      body: JSON.stringify(packageData),
    })
  }

  async deletePackage(id: number) {
    return this.request(`/packages/${id}`, {
      method: 'DELETE',
    })
  }

  // Customers
  async getCustomers() {
    return this.request('/customers')
  }

  async createCustomer(customerData: any) {
    return this.request('/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    })
  }

  async updateCustomer(id: number, customerData: any) {
    return this.request(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    })
  }

  // Subscriptions
  async getSubscriptions() {
    return this.request('/subscriptions')
  }

  async createSubscription(subscriptionData: any) {
    return this.request('/subscriptions', {
      method: 'POST',
      body: JSON.stringify(subscriptionData),
    })
  }

  // Production Reports
  async getTodayAggregatedReport() {
    return this.request('/production/today/aggregated')
  }

  async getTodayCustomerBreakdown() {
    return this.request('/production/today/customer-breakdown')
  }

  // Customer specific endpoints
  async getCustomerSubscription(customerId: number) {
    return this.request(`/customers/${customerId}/subscription`)
  }

  async getCustomerPreferences(customerId: number) {
    return this.request(`/customers/${customerId}/preferences`)
  }

  async updateCustomerPreferences(customerId: number, preferences: any) {
    return this.request(`/customers/${customerId}/preferences`, {
      method: 'PUT',
      body: JSON.stringify(preferences),
    })
  }

  async pauseSubscription(customerId: number, pauseData: any) {
    return this.request(`/customers/${customerId}/subscription/pause`, {
      method: 'POST',
      body: JSON.stringify(pauseData),
    })
  }

  async getAvailablePauseDates(customerId: number) {
    return this.request(`/customers/${customerId}/subscription/available-pause-dates`)
  }
}

export const apiClient = new ApiClient()
