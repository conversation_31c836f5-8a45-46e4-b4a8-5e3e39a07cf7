'use client'

import { AdminLayout } from '@/components/layout/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function AdminDashboard() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم المدير</h1>
          <p className="mt-2 text-gray-600">
            مرحباً بك في نظام إدارة العملاء والإنتاج لمطعم برو دايت
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground">
                العملاء النشطين
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الاشتراكات النشطة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground">
                اشتراكات قيد التشغيل
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الباقات المتاحة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground">
                باقات الوجبات
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">وجبات اليوم</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground">
                وجبات للتحضير
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>الإجراءات السريعة</CardTitle>
              <CardDescription>
                الوصول السريع للمهام الأساسية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-1 gap-2">
                <a 
                  href="/admin/customers" 
                  className="p-3 text-right border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium">إضافة عميل جديد</div>
                  <div className="text-sm text-gray-600">إنشاء حساب عميل جديد</div>
                </a>
                <a 
                  href="/admin/packages" 
                  className="p-3 text-right border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium">إدارة الباقات</div>
                  <div className="text-sm text-gray-600">إضافة أو تعديل باقات الوجبات</div>
                </a>
                <a 
                  href="/admin/production" 
                  className="p-3 text-right border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium">تقرير الإنتاج</div>
                  <div className="text-sm text-gray-600">عرض تقرير إنتاج اليوم</div>
                </a>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>النشاط الأخير</CardTitle>
              <CardDescription>
                آخر العمليات في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-gray-500 py-8">
                لا توجد أنشطة حديثة
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
